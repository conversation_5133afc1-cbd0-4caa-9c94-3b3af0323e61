package com.liuyang.service.impl;

import com.liuyang.config.DatabaseConfigForMybatis;
import com.liuyang.dto.*;
import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.exception.DatabaseException;
import com.liuyang.service.*;
import com.liuyang.util.SnowflakeIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.liuyang.controller.LogWebSocketController;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 跨数据库表复制服务
 * 支持PostgreSQL到MySQL的跨数据库迁移，包括双主键设计
 */
@Service
public class CrossDatabaseTableCopyServiceImpl implements CrossDatabaseTableCopyService {
    
    private static final Logger logger = LoggerFactory.getLogger(CrossDatabaseTableCopyService.class);
    
    @Autowired
    private DatabaseConfigForMybatis databaseConfig;
    
    @Autowired
    private TableService tableService;
    
    @Autowired
    private CrossDatabaseTypeMapperService typeMapper;
    
    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    
    @Autowired
    private EnhancedFieldMappingService fieldMappingService;
    
    @Autowired(required = false)
    private AiFieldNamingService aiFieldNamingService;

    @Autowired(required = false)
    private TableNameOptimizationService tableNameOptimizationService;

    @Autowired
    private MigrationMappingService migrationMappingService;
    
    @Autowired
    private DatabaseConfigManagementService databaseConfigManagementService;

    @Autowired
    private BigDataMigrationService bigDataMigrationService;

    @Autowired
    @Qualifier("highPerformanceMigrationService")
    private BigDataMigrationService highPerformanceMigrationService;
    
    /**
     * 跨数据库复制表
     */
    public void crossDatabaseCopyTable(CopyTableRequest request) {
        // 自动查找并设置配置ID
        autoFillConfigIds(request);
        logger.info("开始跨数据库复制表: {}", request);
        
        // 验证请求
        validateRequest(request);
        
        Long migrationBatchId = null;
        long sourceRecordCount = 0;
        long targetRecordCount = 0;
        String targetDdlStatement = null;
        
        try {
            // 1. 获取源表结构
            TableStructure sourceStructure = tableService.getTableStructure(
                request.getSourceDatabase(), 
                request.getTableName()
            );
            
            // 2. 转换表结构（支持双主键设计）
            TableStructure targetStructure = convertTableStructure(
                sourceStructure, 
                request
            );
            
            // 3. 生成DDL语句
            targetDdlStatement = generateMySQLCreateTableSql(request.getTargetTableName(), targetStructure);
            
            // 4. 创建目标表
            createTargetTable(request, targetStructure);
            
            // 5. 复制数据（如果需要）
            if (request.isCopyData()) {
                sourceRecordCount = getSourceRecordCount(request);

                // 智能选择迁移策略
                if (sourceRecordCount > 1000000) {
                    String msg = String.format("🚀 检测到百万级数据量(%,d 条)，使用企业级高性能迁移策略", sourceRecordCount);
                    logger.info(msg);
                    LogWebSocketController.addLog("INFO", "CrossDatabaseTableCopy", msg);
                    highPerformanceMigrationService.migrateLargeDataset(request, sourceStructure, targetStructure);
                } else if (sourceRecordCount > 50000) {
                    String msg = String.format("⚡ 检测到大数据量(%,d 条)，使用优化迁移策略", sourceRecordCount);
                    logger.info(msg);
                    LogWebSocketController.addLog("INFO", "CrossDatabaseTableCopy", msg);
                    bigDataMigrationService.migrateLargeDataset(request, sourceStructure, targetStructure);
                } else {
                    String msg = String.format("📋 使用标准迁移策略(%,d 条)", sourceRecordCount);
                    logger.info(msg);
                    LogWebSocketController.addLog("INFO", "CrossDatabaseTableCopy", msg);
                    copyTableData(request, sourceStructure, targetStructure);
                }

                targetRecordCount = getTargetRecordCount(request);
            }
            
            // 6. 记录迁移成功（包含AI提示词）
            String aiPromptDescription = getAiPromptDescriptionForTable(request, sourceStructure);
            migrationBatchId = migrationMappingService.recordTableMigrationWithAiPrompt(
                request.getSourceConfigId(),
                request.getTargetConfigId(),
                request.getSourceDatabase(),
                request.getTargetDatabase(),
                request.getTableName(),
                request.getTargetTableName(),
                targetDdlStatement,
                aiPromptDescription,  // 传递AI提示词描述
                sourceStructure.getColumns(),
                targetStructure.getColumns(),
                sourceRecordCount,
                targetRecordCount,
                true,
                null
            );
            
            logger.info("跨数据库表复制完成: {} -> {}, 迁移批次ID: {}", 
                      request.getTableName(), request.getTargetTableName(), migrationBatchId);
            
        } catch (Exception e) {
            logger.error("❌ 跨数据库复制表失败 - 表名: {}, 错误类型: {}, 错误消息: {}",
                        request.getTableName(), e.getClass().getSimpleName(), e.getMessage(), e);

            // 记录迁移失败
            try {
                if (migrationBatchId == null) {
                    // 如果还没有创建迁移记录，创建一个失败的记录
                    TableStructure sourceStructure = tableService.getTableStructure(
                        request.getSourceDatabase(), 
                        request.getTableName()
                    );
                    TableStructure targetStructure = convertTableStructure(sourceStructure, request);
                    if (targetDdlStatement == null) {
                        targetDdlStatement = generateMySQLCreateTableSql(request.getTargetTableName(), targetStructure);
                    }
                    
                    String aiPromptDescription = getAiPromptDescriptionForTable(request, sourceStructure);
                    migrationMappingService.recordTableMigrationWithAiPrompt(
                        request.getSourceConfigId(),
                        request.getTargetConfigId(),
                        request.getSourceDatabase(),
                        request.getTargetDatabase(),
                        request.getTableName(),
                        request.getTargetTableName(),
                        targetDdlStatement,
                        aiPromptDescription,  // 传递AI提示词描述
                        sourceStructure.getColumns(),
                        targetStructure.getColumns(),
                        sourceRecordCount,
                        0,
                        false,
                        e.getMessage()
                    );
                }
            } catch (Exception recordException) {
                logger.error("记录迁移失败信息时出错", recordException);
            }
            
            throw new DatabaseException("跨数据库复制表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证请求
     */
    private void validateRequest(CopyTableRequest request) {
        if (!request.isCrossDatabaseMigration()) {
            throw new DatabaseException("此服务仅支持跨数据库迁移");
        }
        
        if (request.getSourceDbType() != DatabaseType.POSTGRESQL) {
            throw new DatabaseException("目前仅支持从PostgreSQL迁移");
        }
        
        if (request.getTargetDbType() != DatabaseType.MYSQL) {
            throw new DatabaseException("目前仅支持迁移到MySQL");
        }
    }
    
    /**
     * 转换表结构（支持双主键设计和AI字段映射）
     */
    private TableStructure convertTableStructure(TableStructure sourceStructure, CopyTableRequest request) {
        TableStructure targetStructure = new TableStructure();
        targetStructure.setTableName(request.getTargetTableName());

        // 获取AI提示词描述并生成表注释
        String aiPromptDescription = getAiPromptDescriptionForTable(request, sourceStructure);
        String tableComment = generateTableCommentFromPrompt(aiPromptDescription, request);
        targetStructure.setTableComment(tableComment);
        
        List<ColumnInfo> targetColumns = new ArrayList<>();
        List<String> targetPrimaryKeys = new ArrayList<>();
        
        // 获取字段映射（优先使用前端预生成的映射）
        Map<String, String> fieldMappings = getFieldMappings(sourceStructure, request);
        logger.info("获取到 {} 个字段映射", fieldMappings.size());
        
        // 验证字段映射的有效性
        validateFieldMappings(fieldMappings);
        
        // 如果启用双主键设计，先添加新的主键列
        if (request.isUseDualPrimaryKey()) {
            ColumnInfo newPrimaryKey = createNewPrimaryKeyColumn(request.getNewPrimaryKeyFieldName());
            targetColumns.add(newPrimaryKey);
            targetPrimaryKeys.add(request.getNewPrimaryKeyFieldName());
        }
        
        // 处理原有列
        String originalPrimaryKey = null;
        if (sourceStructure.getPrimaryKeys() != null && !sourceStructure.getPrimaryKeys().isEmpty()) {
            originalPrimaryKey = sourceStructure.getPrimaryKeys().get(0); // 假设只有一个主键
        }
        
        for (ColumnInfo sourceColumn : sourceStructure.getColumns()) {
            ColumnInfo targetColumn = convertColumn(sourceColumn, request, fieldMappings);
            
            // 如果这是原来的主键列且启用了双主键设计
            if (request.isUseDualPrimaryKey() && 
                originalPrimaryKey != null && 
                sourceColumn.getColumnName().equals(originalPrimaryKey)) {
                
                // 重命名为业务主键
                targetColumn.setColumnName(request.getBusinessKeyFieldName());
                targetColumn.setPrimaryKey(false); // 不再是主键，只是普通列
                targetColumn.setNullable(true); // 允许为空
            }
            
            targetColumns.add(targetColumn);
        }
        
        // 如果没有启用双主键设计，保持原有主键（但使用映射后的字段名）
        if (!request.isUseDualPrimaryKey()) {
            List<String> mappedPrimaryKeys = new ArrayList<>();
            if (sourceStructure.getPrimaryKeys() != null) {
                for (String originalKey : sourceStructure.getPrimaryKeys()) {
                    String mappedKey = fieldMappings.getOrDefault(originalKey, originalKey);
                    mappedPrimaryKeys.add(mappedKey);
                }
            }
            targetPrimaryKeys.addAll(mappedPrimaryKeys);
        }
        
        targetStructure.setColumns(targetColumns);
        targetStructure.setPrimaryKeys(targetPrimaryKeys);
        
        return targetStructure;
    }
    
    /**
     * 获取字段映射（优先使用前端预生成的映射，避免重复调用AI）
     */
    private Map<String, String> getFieldMappings(TableStructure sourceStructure, CopyTableRequest request) {
        Map<String, String> mappings = new HashMap<>();
        
        // 优先使用前端传递的字段映射
        if (request.getFieldMappings() != null && !request.getFieldMappings().isEmpty()) {
            logger.info("使用前端预生成的字段映射，避免重复调用AI");
            
            // 用于检查目标字段名重复
            Set<String> usedTargetFields = new HashSet<>();
            Map<String, Integer> fieldNameCounters = new HashMap<>();
            
            for (EnhancedFieldMapping mapping : request.getFieldMappings()) {
                String sourceField = mapping.getSourceField();
                String targetField = mapping.getTargetField();
                
                if (targetField != null && !targetField.trim().isEmpty()) {
                    String finalTargetField = targetField.trim();
                    
                    // 检查目标字段名是否重复
                    if (usedTargetFields.contains(finalTargetField)) {
                        // 如果重复，添加序号后缀
                        int counter = fieldNameCounters.getOrDefault(finalTargetField, 1) + 1;
                        fieldNameCounters.put(finalTargetField, counter);
                        finalTargetField = finalTargetField + "_" + counter;
                        
                        logger.warn("检测到重复目标字段名，自动重命名: {} -> {} (原字段: {})", 
                            targetField, finalTargetField, sourceField);
                    }
                    
                    usedTargetFields.add(finalTargetField);
                    mappings.put(sourceField, finalTargetField);
                    
                    if (!sourceField.equals(finalTargetField)) {
                        logger.info("字段映射: {} -> {}", sourceField, finalTargetField);
                    }
                } else {
                    // 如果目标字段为空，使用源字段名
                    String finalTargetField = sourceField;
                    
                    // 同样检查源字段名是否重复
                    if (usedTargetFields.contains(finalTargetField)) {
                        int counter = fieldNameCounters.getOrDefault(finalTargetField, 1) + 1;
                        fieldNameCounters.put(finalTargetField, counter);
                        finalTargetField = finalTargetField + "_" + counter;
                        
                        logger.warn("检测到重复字段名，自动重命名: {} -> {}", 
                            sourceField, finalTargetField);
                    }
                    
                    usedTargetFields.add(finalTargetField);
                    mappings.put(sourceField, finalTargetField);
                }
            }
            
            logger.info("字段映射完成，共处理 {} 个字段，检测到 {} 个重复字段名", 
                mappings.size(), fieldNameCounters.size());
            return mappings;
        }
        
        // 如果前端没有传递映射，才调用AI分析（兼容性处理）
        logger.info("前端未提供字段映射，将调用AI分析");
        try {
            // 使用AI分析字段映射
            List<EnhancedFieldMapping> enhancedMappings = fieldMappingService.analyzeFieldMappings(
                sourceStructure, 
                "数据库跨平台迁移优化"
            );
            
            // 用于检查目标字段名重复
            Set<String> usedTargetFields = new HashSet<>();
            Map<String, Integer> fieldNameCounters = new HashMap<>();
            
            // 转换为简单的字段映射Map
            for (EnhancedFieldMapping mapping : enhancedMappings) {
                String sourceField = mapping.getSourceField();
                String targetField = mapping.getTargetField();
                
                if (targetField != null && !targetField.trim().isEmpty()) {
                    String finalTargetField = targetField.trim();
                    
                    // 检查目标字段名是否重复
                    if (usedTargetFields.contains(finalTargetField)) {
                        // 如果重复，添加序号后缀
                        int counter = fieldNameCounters.getOrDefault(finalTargetField, 1) + 1;
                        fieldNameCounters.put(finalTargetField, counter);
                        finalTargetField = finalTargetField + "_" + counter;
                        
                        logger.warn("AI分析检测到重复目标字段名，自动重命名: {} -> {} (原字段: {})", 
                            targetField, finalTargetField, sourceField);
                    }
                    
                    usedTargetFields.add(finalTargetField);
                    mappings.put(sourceField, finalTargetField);
                    
                    if (!sourceField.equals(finalTargetField)) {
                        logger.info("字段映射: {} -> {}", sourceField, finalTargetField);
                    }
                } else {
                    // 如果没有映射，使用原字段名
                    String finalTargetField = sourceField;
                    
                    // 同样检查源字段名是否重复
                    if (usedTargetFields.contains(finalTargetField)) {
                        int counter = fieldNameCounters.getOrDefault(finalTargetField, 1) + 1;
                        fieldNameCounters.put(finalTargetField, counter);
                        finalTargetField = finalTargetField + "_" + counter;
                        
                        logger.warn("AI分析检测到重复字段名，自动重命名: {} -> {}", 
                            sourceField, finalTargetField);
                    }
                    
                    usedTargetFields.add(finalTargetField);
                    mappings.put(sourceField, finalTargetField);
                }
            }
            
            logger.info("AI字段映射完成，共处理 {} 个字段，检测到 {} 个重复字段名", 
                mappings.size(), fieldNameCounters.size());
            
        } catch (Exception e) {
            logger.warn("获取字段映射失败，将使用原始字段名: {}", e.getMessage());
            // 如果失败，使用原始字段名
            for (ColumnInfo column : sourceStructure.getColumns()) {
                mappings.put(column.getColumnName(), column.getColumnName());
            }
        }
        
        return mappings;
    }
    
    /**
     * 验证字段映射的有效性
     */
    private void validateFieldMappings(Map<String, String> mappings) {
        if (mappings == null || mappings.isEmpty()) {
            throw new DatabaseException("字段映射为空");
        }
        
        // 检查是否有重复的目标字段名
        Set<String> targetFields = new HashSet<>(mappings.values());
        if (targetFields.size() != mappings.size()) {
            logger.error("检测到重复的目标字段名，映射数量: {}, 唯一目标字段数量: {}", 
                mappings.size(), targetFields.size());
            
            // 详细记录重复字段
            Map<String, List<String>> duplicateTargets = new HashMap<>();
            for (Map.Entry<String, String> entry : mappings.entrySet()) {
                String targetField = entry.getValue();
                duplicateTargets.computeIfAbsent(targetField, k -> new ArrayList<>()).add(entry.getKey());
            }
            
            for (Map.Entry<String, List<String>> entry : duplicateTargets.entrySet()) {
                if (entry.getValue().size() > 1) {
                    logger.error("重复的目标字段 '{}' 来自源字段: {}", entry.getKey(), entry.getValue());
                }
            }
            
            throw new DatabaseException("字段映射包含重复的目标字段名，这将导致创建表失败");
        }
        
        logger.info("字段映射验证通过，共 {} 个有效映射", mappings.size());
    }
    
    /**
     * 创建新主键列
     */
    private ColumnInfo createNewPrimaryKeyColumn(String columnName) {
        ColumnInfo column = new ColumnInfo();
        column.setColumnName(columnName);
        column.setDataType("BIGINT");
        column.setColumnSize(20);
        column.setDecimalDigits(0);
        column.setNullable(false);
        column.setPrimaryKey(true);
        column.setAutoIncrement(false); // 使用雪花算法，不使用自增
        column.setDefaultValue(null);
        column.setComment("系统生成的主键(雪花算法)");
        return column;
    }
    
    /**
     * 转换列信息（支持AI生成字段注释）
     */
    private ColumnInfo convertColumn(ColumnInfo sourceColumn, CopyTableRequest request, Map<String, String> fieldMappings) {
        ColumnInfo targetColumn = new ColumnInfo();
        
        // 使用AI映射后的字段名
        String sourceFieldName = sourceColumn.getColumnName();
        String targetFieldName = fieldMappings.getOrDefault(sourceFieldName, sourceFieldName);
        targetColumn.setColumnName(targetFieldName);
        
        // 优先使用前端预生成的字段注释，避免重复调用AI
        String fieldComment = getFieldCommentFromRequest(sourceFieldName, request);
        if (fieldComment != null && !fieldComment.trim().isEmpty()) {
            targetColumn.setComment(fieldComment);
            logger.debug("使用前端预生成的字段注释: {} -> \"{}\"", targetFieldName, fieldComment);
        } else {
            // 如果前端没有提供注释，才使用AI生成
            String aiGeneratedComment = generateFieldComment(sourceColumn, targetFieldName, request);
            if (aiGeneratedComment != null && !aiGeneratedComment.trim().isEmpty()) {
                targetColumn.setComment(aiGeneratedComment);
            } else {
                // 降级方案：使用原有注释
                targetColumn.setComment(sourceColumn.getComment());
            }
        }
        
        targetColumn.setNullable(sourceColumn.isNullable());
        targetColumn.setPrimaryKey(sourceColumn.isPrimaryKey());
        targetColumn.setAutoIncrement(false); // MySQL中不使用自增
        
        // 转换数据类型
        String targetDataType = typeMapper.mapDataType(
            request.getSourceDbType(),
            request.getTargetDbType(),
            sourceColumn.getDataType(),
            sourceColumn.getColumnSize(),
            sourceColumn.getDecimalDigits()
        );
        
        targetColumn.setDataType(targetDataType);
        
        // 处理列大小和小数位数
        if (targetDataType.contains("(")) {
            // 如果类型已经包含大小信息，不再设置
            targetColumn.setColumnSize(null);
            targetColumn.setDecimalDigits(null);
        } else {
            targetColumn.setColumnSize(sourceColumn.getColumnSize());
            targetColumn.setDecimalDigits(sourceColumn.getDecimalDigits());
        }
        
        // 处理默认值（可能需要转换）
        targetColumn.setDefaultValue(convertDefaultValue(sourceColumn.getDefaultValue(), request));
        
        return targetColumn;
    }
    
    /**
     * 从请求中获取字段注释（前端预生成的字段描述）
     */
    private String getFieldCommentFromRequest(String sourceFieldName, CopyTableRequest request) {
        if (request.getFieldMappings() == null || request.getFieldMappings().isEmpty()) {
            return null;
        }
        
        // 在前端传递的字段映射中查找对应的注释
        for (EnhancedFieldMapping mapping : request.getFieldMappings()) {
            if (sourceFieldName.equals(mapping.getSourceField())) {
                String comment = mapping.getComment();
                if (comment != null && !comment.trim().isEmpty()) {
                    return comment.trim();
                }
                break;
            }
        }
        
        return null;
    }
    
    /**
     * 使用AI生成字段注释
     */
    private String generateFieldComment(ColumnInfo sourceColumn, String targetFieldName, CopyTableRequest request) {
        try {
            // 如果AI服务不可用，返回null使用降级方案
            if (aiFieldNamingService == null) {
                logger.debug("AI服务不可用，跳过字段注释生成: {}", targetFieldName);
                return null;
            }
            
            // 使用AI服务生成字段注释
            String businessContext = String.format("数据库跨平台迁移（PostgreSQL -> MySQL），目标表：%s", 
                request.getTargetTableName());
            
            String aiComment = aiFieldNamingService.generateFieldComment(
                targetFieldName,
                sourceColumn.getDataType(),
                sourceColumn.getComment(),
                businessContext
            );
            
            if (aiComment != null && !aiComment.trim().isEmpty()) {
                logger.info("AI生成字段注释: {} -> \"{}\"", targetFieldName, aiComment);
                return aiComment;
            }
            
        } catch (Exception e) {
            logger.warn("AI生成字段注释失败，使用降级方案: {} - {}", targetFieldName, e.getMessage());
        }
        
        return null; // 返回null使用降级方案
    }
    
    /**
     * 转换默认值
     */
    private String convertDefaultValue(String defaultValue, CopyTableRequest request) {
        if (defaultValue == null || defaultValue.trim().isEmpty()) {
            return null;
        }
        
        // PostgreSQL到MySQL的默认值转换
        String trimmed = defaultValue.trim();
        
        // 处理PostgreSQL的NOW()函数
        if (trimmed.toLowerCase().contains("now()") || trimmed.toLowerCase().contains("current_timestamp")) {
            return "CURRENT_TIMESTAMP";
        }
        
        // 处理PostgreSQL的布尔值
        if (trimmed.equalsIgnoreCase("true") || trimmed.equalsIgnoreCase("'t'")) {
            return "1";
        }
        if (trimmed.equalsIgnoreCase("false") || trimmed.equalsIgnoreCase("'f'")) {
            return "0";
        }
        
        // 移除PostgreSQL特有的类型转换
        if (trimmed.contains("::")) {
            trimmed = trimmed.substring(0, trimmed.indexOf("::"));
        }
        
        return trimmed;
    }
    
    /**
     * 创建目标表
     */
    private void createTargetTable(CopyTableRequest request, TableStructure structure) {
        try (Connection conn = getConnection(request.getTargetDbType(), request.getTargetDatabase())) {
            
            // 如果需要，先删除已存在的表
            if (request.isDropIfExists()) {
                String dropSql = "DROP TABLE IF EXISTS " + request.getTargetTableName();
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute(dropSql);
                    logger.info("删除已存在的表: {}", request.getTargetTableName());
                }
            }
            
            // 生成创建表的SQL
            String createTableSql = generateMySQLCreateTableSql(request.getTargetTableName(), structure);
            logger.debug("创建表SQL: {}", createTableSql);
            
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createTableSql);
                logger.info("创建表成功: {}", request.getTargetTableName());
            }
            
        } catch (SQLException e) {
            logger.error("创建表失败", e);
            throw new DatabaseException("创建表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成MySQL的创建表SQL
     */
    private String generateMySQLCreateTableSql(String tableName, TableStructure structure) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ").append(tableName).append(" (\n");
        
        List<String> columnDefinitions = new ArrayList<>();
        
        // 添加列定义
        for (ColumnInfo column : structure.getColumns()) {
            StringBuilder columnDef = new StringBuilder();
            columnDef.append("    ").append(column.getColumnName()).append(" ");
            columnDef.append(column.getDataType());
            
            // 是否允许NULL
            if (!column.isNullable()) {
                columnDef.append(" NOT NULL");
            }
            
            // 默认值
            if (column.getDefaultValue() != null && !column.getDefaultValue().trim().isEmpty()) {
                columnDef.append(" DEFAULT ").append(column.getDefaultValue());
            }
            
            // 列注释
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                columnDef.append(" COMMENT '").append(column.getComment().replace("'", "\\'")).append("'");
            }
            
            columnDefinitions.add(columnDef.toString());
        }
        
        sql.append(String.join(",\n", columnDefinitions));
        
        // 添加主键约束
        if (structure.getPrimaryKeys() != null && !structure.getPrimaryKeys().isEmpty()) {
            sql.append(",\n    PRIMARY KEY (");
            sql.append(String.join(", ", structure.getPrimaryKeys()));
            sql.append(")");
        }
        
        sql.append("\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // 添加表注释
        if (structure.getTableComment() != null && !structure.getTableComment().trim().isEmpty()) {
            sql.append(" COMMENT='").append(structure.getTableComment().replace("'", "\\'")).append("'");
        }
        
        return sql.toString();
    }
    
    /**
     * 复制表数据
     */
    private void copyTableData(CopyTableRequest request, TableStructure sourceStructure, TableStructure targetStructure) {
        String startMsg = String.format("📋 开始标准迁移: %s -> %s", request.getTableName(), request.getTargetTableName());
        logger.info(startMsg);
        LogWebSocketController.addLog("INFO", "StandardMigration", startMsg);

        try (Connection sourceConn = getConnection(request.getSourceDbType(), request.getSourceDatabase());
             Connection targetConn = getConnection(request.getTargetDbType(), request.getTargetDatabase())) {

            // 设置自动提交为false，使用事务
            targetConn.setAutoCommit(false);

            try {
                // 获取源表数据总数
                long totalRows = getTotalRows(sourceConn, request.getTableName());
                logger.info("源表总行数: {}", totalRows);

                if (totalRows == 0) {
                    logger.info("源表无数据，跳过数据复制");
                    return;
                }

                // 生成列映射
                ColumnMapping columnMapping = createColumnMapping(sourceStructure, targetStructure, request);

                // 生成查询和插入SQL
                String selectSql = generateSelectSql(request.getTableName(), columnMapping.getSourceColumns());
                String insertSql = generateInsertSql(request.getTargetTableName(), columnMapping.getTargetColumns());

                logger.debug("查询SQL: {}", selectSql);
                logger.debug("插入SQL: {}", insertSql);

                // 分批复制数据
                int batchSize = databaseConfig.getBatchSize();
                long copiedRows = 0;
                int offset = 0;

                while (copiedRows < totalRows) {
                    String pagedSelectSql = selectSql + " LIMIT " + batchSize + " OFFSET " + offset;

                    try (PreparedStatement selectStmt = sourceConn.prepareStatement(pagedSelectSql);
                         PreparedStatement insertStmt = targetConn.prepareStatement(insertSql);
                         ResultSet rs = selectStmt.executeQuery()) {

                        int batchCount = 0;
                        while (rs.next()) {
                            setInsertParameters(rs, insertStmt, columnMapping, request);
                            insertStmt.addBatch();
                            batchCount++;
                        }

                        if (batchCount > 0) {
                            insertStmt.executeBatch();
                            copiedRows += batchCount;
                            logger.info("已复制 {} / {} 行数据", copiedRows, totalRows);
                        }

                        offset += batchSize;

                        // 如果这批数据少于batchSize，说明已经是最后一批
                        if (batchCount < batchSize) {
                            break;
                        }
                    }
                }

                targetConn.commit();
                String completeMsg = String.format("✅ 标准迁移完成，共复制 %,d 行数据", copiedRows);
                logger.info(completeMsg);
                LogWebSocketController.addLog("INFO", "StandardMigration", completeMsg);

            } catch (SQLException e) {
                targetConn.rollback();
                throw e;
            }

        } catch (SQLException e) {
            logger.error("复制表数据失败", e);
            throw new DatabaseException("复制表数据失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建列映射关系（使用AI优化后的字段名）
     */
    private ColumnMapping createColumnMapping(TableStructure sourceStructure, TableStructure targetStructure, CopyTableRequest request) {
        List<String> sourceColumns = new ArrayList<>();
        List<String> targetColumns = new ArrayList<>();
        
        // 获取原主键列名
        String originalPrimaryKey = null;
        if (sourceStructure.getPrimaryKeys() != null && !sourceStructure.getPrimaryKeys().isEmpty()) {
            originalPrimaryKey = sourceStructure.getPrimaryKeys().get(0);
        }
        
        // 创建目标列映射（排除新主键列）
        List<ColumnInfo> targetColumnsForMapping = new ArrayList<>();
        for (ColumnInfo targetColumn : targetStructure.getColumns()) {
            // 如果使用双主键设计，跳过新主键列
            if (request.isUseDualPrimaryKey() && 
                targetColumn.getColumnName().equals(request.getNewPrimaryKeyFieldName())) {
                continue;
            }
            targetColumnsForMapping.add(targetColumn);
        }
        
        // 映射源列到目标列
        for (int i = 0; i < sourceStructure.getColumns().size(); i++) {
            ColumnInfo sourceColumn = sourceStructure.getColumns().get(i);
            sourceColumns.add(sourceColumn.getColumnName());
            
            // 获取对应的目标列名（AI优化后的字段名）
            if (i < targetColumnsForMapping.size()) {
                ColumnInfo targetColumn = targetColumnsForMapping.get(i);
                targetColumns.add(targetColumn.getColumnName());
            } else {
                // 如果映射失败，使用源列名作为降级方案
                targetColumns.add(sourceColumn.getColumnName());
                logger.warn("列映射不匹配，使用源列名: {}", sourceColumn.getColumnName());
            }
        }
        
        return new ColumnMapping(sourceColumns, targetColumns, request.isUseDualPrimaryKey(), request.getNewPrimaryKeyFieldName());
    }
    
    /**
     * 设置插入参数
     */
    private void setInsertParameters(ResultSet rs, PreparedStatement insertStmt, ColumnMapping mapping, CopyTableRequest request) throws SQLException {
        int paramIndex = 1;
        
        // 如果使用双主键设计，先设置新主键
        if (request.isUseDualPrimaryKey()) {
            long newId = snowflakeIdGenerator.nextId();
            insertStmt.setLong(paramIndex++, newId);
        }
        
        // 设置其他列的值
        for (int i = 0; i < mapping.getSourceColumns().size(); i++) {
            Object value = rs.getObject(i + 1);
            insertStmt.setObject(paramIndex++, value);
        }
    }
    
    /**
     * 获取数据库连接
     */
    private Connection getConnection(DatabaseType dbType, String databaseName) throws SQLException {
        try {
            DatabaseConfigEntity config;

            // 根据数据库类型获取相应的配置
            if (dbType == DatabaseType.POSTGRESQL) {
                config = databaseConfigManagementService.getSourceDatabaseConfig();
            } else if (dbType == DatabaseType.MYSQL) {
                config = databaseConfigManagementService.getTargetDatabaseConfig();
            } else {
                throw new SQLException("不支持的数据库类型: " + dbType);
            }

            String url = config.getJdbcUrl(databaseName);

            // 添加连接超时参数，特别针对阿里云RDS
            if (dbType == DatabaseType.MYSQL) {
                if (url.contains("?")) {
                    url += "&connectTimeout=120000&socketTimeout=300000&autoReconnect=true&failOverReadOnly=false&maxReconnects=3&useSSL=false";
                } else {
                    url += "?connectTimeout=120000&socketTimeout=300000&autoReconnect=true&failOverReadOnly=false&maxReconnects=3&useSSL=false";
                }
            } else if (dbType == DatabaseType.POSTGRESQL) {
                if (url.contains("?")) {
                    url += "&connectTimeout=120&socketTimeout=300&loginTimeout=120";
                } else {
                    url += "?connectTimeout=120&socketTimeout=300&loginTimeout=120";
                }
            }

            logger.info("🔗 连接数据库: {}", url.replaceAll("password=[^&]*", "password=***"));

            // 设置DriverManager超时
            DriverManager.setLoginTimeout(120); // 2分钟连接超时

            long startTime = System.currentTimeMillis();
            Connection conn = DriverManager.getConnection(url, config.getUsername(), config.getPassword());
            long connectionTime = System.currentTimeMillis() - startTime;

            logger.info("✅ 数据库连接成功: {} (耗时: {}ms)", dbType, connectionTime);
            return conn;

        } catch (Exception e) {
            logger.error("❌ 数据库连接失败: dbType={}, database={}, error={}", dbType, databaseName, e.getMessage());
            throw new SQLException("获取数据库连接失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取表总行数
     */
    private long getTotalRows(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
        }
        return 0;
    }
    
    /**
     * 生成查询SQL
     */
    private String generateSelectSql(String tableName, List<String> columnNames) {
        return "SELECT " + String.join(", ", columnNames) + " FROM " + tableName;
    }
    
    /**
     * 生成插入SQL
     */
    private String generateInsertSql(String tableName, List<String> columnNames) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        sql.append(String.join(", ", columnNames));
        sql.append(") VALUES (");

        List<String> placeholders = new ArrayList<>();
        for (int i = 0; i < columnNames.size(); i++) {
            placeholders.add("?");
        }
        sql.append(String.join(", ", placeholders));
        sql.append(")");

        return sql.toString();
    }
    
    /**
     * 获取源表记录数
     */
    private long getSourceRecordCount(CopyTableRequest request) {
        try (Connection conn = getConnection(request.getSourceDbType(), request.getSourceDatabase())) {
            return getTotalRows(conn, request.getTableName());
        } catch (SQLException e) {
            logger.error("获取源表记录数失败: {}", request.getTableName(), e);
            return 0;
        }
    }
    
    /**
     * 获取目标表记录数
     */
    private long getTargetRecordCount(CopyTableRequest request) {
        try (Connection conn = getConnection(request.getTargetDbType(), request.getTargetDatabase())) {
            return getTotalRows(conn, request.getTargetTableName());
        } catch (SQLException e) {
            logger.error("获取目标表记录数失败: {}", request.getTargetTableName(), e);
            return 0;
        }
    }
    
    /**
     * 自动填充配置ID
     */
    private void autoFillConfigIds(CopyTableRequest request) {
        logger.info("开始自动查找数据库配置ID - 源数据库: {}, 目标数据库: {}", 
                   request.getSourceDatabase(), request.getTargetDatabase());
        logger.info("当前配置ID状态 - 源配置ID: {}, 目标配置ID: {}", 
                   request.getSourceConfigId(), request.getTargetConfigId());
        
        if (request.getSourceConfigId() == null || request.getTargetConfigId() == null) {
            
            // 查找源数据库配置
            if (request.getSourceConfigId() == null) {
                logger.info("查找源数据库配置: 数据库={}, 配置类型=SOURCE", request.getSourceDatabase());
                DatabaseConfigEntity sourceConfig = findConfigByDatabaseName(request.getSourceDatabase(), "SOURCE");
                if (sourceConfig != null) {
                    request.setSourceConfigId(sourceConfig.getId());
                    logger.info("✅ 找到源数据库配置: {} (ID: {})", sourceConfig.getConfigName(), sourceConfig.getId());
                } else {
                    logger.error("❌ 未找到SOURCE类型的数据库配置");
                    // 尝试使用默认的源配置
                    DatabaseConfigEntity defaultSourceConfig = getDefaultSourceConfig();
                    if (defaultSourceConfig != null) {
                        request.setSourceConfigId(defaultSourceConfig.getId());
                        logger.warn("🔄 使用默认源配置: {} (ID: {})", defaultSourceConfig.getConfigName(), defaultSourceConfig.getId());
                    }
                }
            }
            
            // 查找目标数据库配置
            if (request.getTargetConfigId() == null) {
                logger.info("查找目标数据库配置: 数据库={}, 配置类型=TARGET", request.getTargetDatabase());
                DatabaseConfigEntity targetConfig = findConfigByDatabaseName(request.getTargetDatabase(), "TARGET");
                if (targetConfig != null) {
                    request.setTargetConfigId(targetConfig.getId());
                    logger.info("✅ 找到目标数据库配置: {} (ID: {})", targetConfig.getConfigName(), targetConfig.getId());
                } else {
                    logger.error("❌ 未找到TARGET类型的数据库配置");
                    // 尝试使用默认的目标配置
                    DatabaseConfigEntity defaultTargetConfig = getDefaultTargetConfig();
                    if (defaultTargetConfig != null) {
                        request.setTargetConfigId(defaultTargetConfig.getId());
                        logger.warn("🔄 使用默认目标配置: {} (ID: {})", defaultTargetConfig.getConfigName(), defaultTargetConfig.getId());
                    }
                }
            }
        }
        
        logger.info("配置ID查找完成 - 源配置ID: {}, 目标配置ID: {}", 
                   request.getSourceConfigId(), request.getTargetConfigId());
        
        // 验证配置ID是否成功获取
        if (request.getSourceConfigId() == null) {
            throw new DatabaseException("无法找到源数据库配置，请检查数据库配置管理中是否存在类型为SOURCE且数据库类型为POSTGRESQL的激活配置");
        }

        if (request.getTargetConfigId() == null) {
            throw new DatabaseException("无法找到目标数据库配置，请检查数据库配置管理中是否存在类型为TARGET且数据库类型为MYSQL的激活配置");
        }
    }
    
    /**
     * 根据数据库类型和配置类型查找配置
     */
    private DatabaseConfigEntity findConfigByDatabaseName(String databaseName, String configType) {
        try {
            // 获取所有配置
            List<DatabaseConfigEntity> allConfigs = databaseConfigManagementService.getAllConfigs();
            logger.info("总共找到 {} 个数据库配置", allConfigs.size());

            // 根据配置类型确定需要的数据库类型
            String expectedDbType = null;
            if ("SOURCE".equals(configType)) {
                expectedDbType = "POSTGRESQL";  // 源数据库通常是PostgreSQL
            } else if ("TARGET".equals(configType)) {
                expectedDbType = "MYSQL";       // 目标数据库通常是MySQL
            }

            // 查找匹配的配置：配置类型 + 数据库类型 + 激活状态
            for (DatabaseConfigEntity config : allConfigs) {
                logger.debug("检查配置: {} (类型: {}, 数据库类型: {}, 激活: {})",
                           config.getConfigName(), config.getConfigType(), config.getDatabaseType(), config.getIsActive());

                if (config.getConfigType().equals(configType) &&
                    config.getIsActive() &&
                    (expectedDbType == null || expectedDbType.equals(config.getDatabaseType()))) {
                    logger.info("找到匹配的{}配置: {} (数据库类型: {})", configType, config.getConfigName(), config.getDatabaseType());
                    return config;
                }
            }

            logger.warn("未找到匹配的数据库配置: configType={}, expectedDbType={}", configType, expectedDbType);
            return null;
        } catch (Exception e) {
            logger.error("查找数据库配置失败: configType={}", configType, e);
            return null;
        }
    }
    
    /**
     * 获取默认源配置
     */
    private DatabaseConfigEntity getDefaultSourceConfig() {
        try {
            return databaseConfigManagementService.getSourceDatabaseConfig();
        } catch (Exception e) {
            logger.error("获取默认源配置失败", e);
            return null;
        }
    }
    
    /**
     * 获取默认目标配置
     */
    private DatabaseConfigEntity getDefaultTargetConfig() {
        try {
            return databaseConfigManagementService.getTargetDatabaseConfig();
        } catch (Exception e) {
            logger.error("获取默认目标配置失败", e);
            return null;
        }
    }
    
    /**
     * 列映射类
     */
    private static class ColumnMapping {
        private final List<String> sourceColumns;
        private final List<String> targetColumns;
        private final boolean hasDualPrimaryKey;
        private final String newPrimaryKeyFieldName;
        
        public ColumnMapping(List<String> sourceColumns, List<String> targetColumns, boolean hasDualPrimaryKey) {
            this(sourceColumns, targetColumns, hasDualPrimaryKey, "id");
        }
        
        public ColumnMapping(List<String> sourceColumns, List<String> targetColumns, boolean hasDualPrimaryKey, String newPrimaryKeyFieldName) {
            this.sourceColumns = sourceColumns;
            this.targetColumns = targetColumns;
            this.hasDualPrimaryKey = hasDualPrimaryKey;
            this.newPrimaryKeyFieldName = newPrimaryKeyFieldName;
        }
        
        public List<String> getSourceColumns() {
            return sourceColumns;
        }
        
        public List<String> getTargetColumns() {
            // 如果有双主键，需要在前面加上新主键列
            if (hasDualPrimaryKey) {
                List<String> result = new ArrayList<>();
                result.add(newPrimaryKeyFieldName);
                result.addAll(targetColumns);
                return result;
            }
            return targetColumns;
        }
        
        public boolean isHasDualPrimaryKey() {
            return hasDualPrimaryKey;
        }
    }

    /**
     * 获取AI优化表名的提示词描述
     */
    private String getAiPromptDescriptionForTable(CopyTableRequest request, TableStructure sourceStructure) {
        try {
            // 如果请求中包含业务上下文，使用表名优化服务获取AI提示词
            if (request.getBusinessContext() != null && !request.getBusinessContext().trim().isEmpty()) {
                // 注入表名优化服务
                if (tableNameOptimizationService != null) {
                    TableNameOptimizationServiceImpl.TableNameOptimizationResult result =
                        tableNameOptimizationService.optimizeTableName(request.getTableName(), request.getBusinessContext());

                    if (result != null && result.getAiPromptUsed() != null) {
                        logger.info("获取到AI提示词描述，长度: {} 字符", result.getAiPromptUsed().length());
                        return result.getAiPromptUsed();
                    }
                }
            }

            // 如果没有AI提示词，生成一个基本的描述
            return generateBasicTableDescription(request, sourceStructure);

        } catch (Exception e) {
            logger.warn("获取AI提示词描述失败: {}", request.getTableName(), e);
            return generateBasicTableDescription(request, sourceStructure);
        }
    }

    /**
     * 生成基本的表描述信息
     */
    private String generateBasicTableDescription(CopyTableRequest request, TableStructure sourceStructure) {
        StringBuilder description = new StringBuilder();

        // 基本信息
        description.append("数据迁移表: ").append(request.getTableName());

        if (request.getBusinessContext() != null && !request.getBusinessContext().trim().isEmpty()) {
            description.append(" - ").append(request.getBusinessContext());
        }

        // 表结构信息
        if (sourceStructure != null && sourceStructure.getColumns() != null) {
            description.append(" (").append(sourceStructure.getColumns().size()).append("个字段)");
        }

        // 迁移信息
        description.append(" | 从 ").append(request.getSourceDatabase())
                  .append(" 迁移到 ").append(request.getTargetDatabase());

        return description.toString();
    }

    /**
     * 从AI提示词生成MySQL表注释
     */
    private String generateTableCommentFromPrompt(String aiPromptDescription, CopyTableRequest request) {
        try {
            if (aiPromptDescription == null || aiPromptDescription.trim().isEmpty()) {
                return generateFallbackTableComment(request);
            }

            // 从AI提示词中提取业务描述部分
            String businessDescription = extractBusinessDescriptionFromPrompt(aiPromptDescription);

            if (businessDescription != null && !businessDescription.trim().isEmpty()) {
                // 组合业务描述和迁移信息
                return businessDescription + " (从" + request.getSourceDatabase() + "迁移)";
            }

            return generateFallbackTableComment(request);

        } catch (Exception e) {
            logger.warn("从AI提示词生成表注释失败: {}", request.getTableName(), e);
            return generateFallbackTableComment(request);
        }
    }

    /**
     * 从AI提示词中提取业务描述
     */
    private String extractBusinessDescriptionFromPrompt(String aiPromptDescription) {
        try {
            // 查找业务描述部分
            String[] lines = aiPromptDescription.split("\n");
            for (String line : lines) {
                if (line.contains("业务描述:") || line.contains("业务描述：")) {
                    String description = line.substring(line.indexOf(":") + 1).trim();
                    if (description.length() > 0) {
                        return description;
                    }
                }
            }

            // 如果没有找到明确的业务描述，尝试从提示词中提取有用信息
            for (String line : lines) {
                if (line.length() > 10 && !line.contains("转换要求") && !line.contains("示例")
                    && !line.contains("原表名") && !line.contains("请根据")) {
                    return line.trim();
                }
            }

            return null;
        } catch (Exception e) {
            logger.warn("提取业务描述失败", e);
            return null;
        }
    }

    /**
     * 生成降级表注释
     */
    private String generateFallbackTableComment(CopyTableRequest request) {
        return "数据迁移表: " + request.getTableName() +
               " (从" + request.getSourceDatabase() + "迁移到" + request.getTargetDatabase() + ")";
    }
}