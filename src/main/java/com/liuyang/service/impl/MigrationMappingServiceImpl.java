package com.liuyang.service.impl;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.entity.MigrationRecord;
import com.liuyang.entity.TableMappingRecord;
import com.liuyang.entity.FieldMappingRecord;
import com.liuyang.mapper.MigrationRecordMapper;
import com.liuyang.mapper.TableMappingRecordMapper;
import com.liuyang.mapper.FieldMappingRecordMapper;
import com.liuyang.mapper.MigrationRecordMapper.MigrationStatistics;
import com.liuyang.mapper.TableMappingRecordMapper.TableMappingStatistics;
import com.liuyang.mapper.FieldMappingRecordMapper.FieldMappingStatistics;
import com.liuyang.service.MigrationMappingService;
import com.liuyang.util.SnowflakeIdGenerator;
import cn.dev33.satoken.stp.StpUtil;
import com.liuyang.entity.User;
import com.liuyang.entity.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 迁移映射记录服务实现类
 */
@Service
@Transactional
public class MigrationMappingServiceImpl implements MigrationMappingService {
    
    private static final Logger logger = LoggerFactory.getLogger(MigrationMappingServiceImpl.class);
    
    @Autowired
    private MigrationRecordMapper migrationRecordMapper;
    
    @Autowired
    private TableMappingRecordMapper tableMappingRecordMapper;
    
    @Autowired
    private FieldMappingRecordMapper fieldMappingRecordMapper;
    
    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    
    @Override
    public MigrationRecord startMigration(Long sourceConfigId, Long targetConfigId,
                                        String sourceDatabaseName, String targetDatabaseName,
                                        String migrationNotes) {
        try {
            // 使用雪花算法生成唯一的迁移批次ID
            Long migrationBatchId = snowflakeIdGenerator.nextId();
            
            logger.info("生成迁移批次ID: {} (雪花算法生成，长度: {}位)", migrationBatchId, String.valueOf(migrationBatchId).length());
            logger.debug("批次ID优势: 趋势递增、包含时间信息、存储高效、索引友好");
            
            // 创建迁移记录 - 这里可以暂时不设置配置名称和数据库类型，后续可以通过其他服务获取
            MigrationRecord migrationRecord = new MigrationRecord(
                migrationBatchId, sourceConfigId, targetConfigId,
                sourceDatabaseName, targetDatabaseName
            );
            migrationRecord.setMigrationNotes(migrationNotes);

            // 设置操作人信息
            User currentUser = getCurrentUser();
            if (currentUser != null) {
                migrationRecord.setOperatorId(currentUser.getId());
                migrationRecord.setOperatorName(currentUser.getDisplayName());
            }

            // 插入数据库
            int result = migrationRecordMapper.insert(migrationRecord);
            if (result > 0) {
                logger.info("开始迁移记录: 批次ID={}, 源数据库={}, 目标数据库={}", 
                          migrationBatchId, sourceDatabaseName, targetDatabaseName);
                return migrationRecord;
            } else {
                throw new RuntimeException("创建迁移记录失败");
            }
        } catch (Exception e) {
            logger.error("创建迁移记录失败", e);
            throw new RuntimeException("创建迁移记录失败: " + e.getMessage());
        }
    }
    
    @Override
    public void completeMigration(Long migrationBatchId, boolean success, String errorMessage) {
        try {
            MigrationRecord migrationRecord = migrationRecordMapper.selectByBatchId(migrationBatchId);
            if (migrationRecord == null) {
                logger.warn("迁移记录不存在: {}", migrationBatchId);
                return;
            }
            
            // 更新迁移状态
            migrationRecord.markAsCompleted();
            if (!success) {
                migrationRecord.setMigrationStatus("FAILED");
                migrationRecord.setErrorMessage(errorMessage);
            }
            
            // 统计表级别信息
            TableMappingStatistics stats = tableMappingRecordMapper.getTableMappingStatistics(migrationRecord.getId());
            if (stats != null) {
                migrationRecord.setTotalTables(stats.getTotalTables().intValue());
                migrationRecord.setSuccessfulTables(stats.getSuccessfulTables().intValue());
                migrationRecord.setFailedTables(stats.getFailedTables().intValue());
                migrationRecord.setTotalRecords(stats.getTotalTargetRecords());
            }
            
            migrationRecordMapper.updateById(migrationRecord);
            
            logger.info("完成迁移记录: 批次ID={}, 成功={}", migrationBatchId, success);
                      
        } catch (Exception e) {
            logger.error("完成迁移记录失败: " + migrationBatchId, e);
            throw new RuntimeException("完成迁移记录失败: " + e.getMessage());
        }
    }
    
    @Override
    public TableMappingRecord addTableMapping(Long migrationBatchId,
                                            String sourceTableName, String sourceTableSchema, String sourceTableComment,
                                            String targetTableName, String targetTableComment, String targetDdlStatement,
                                            List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns,
                                            long sourceRecordCount, long targetRecordCount) {
        try {
            // 查找迁移记录
            MigrationRecord migrationRecord = migrationRecordMapper.selectByBatchId(migrationBatchId);
            if (migrationRecord == null) {
                throw new RuntimeException("迁移记录不存在: " + migrationBatchId);
            }
            
            // 创建表映射记录，包含冗余字段
            TableMappingRecord tableMappingRecord = new TableMappingRecord(
                migrationBatchId, migrationRecord.getId(), sourceTableName, targetTableName
            );
            tableMappingRecord.setSourceTableSchema(sourceTableSchema);
            tableMappingRecord.setSourceTableComment(sourceTableComment);
            tableMappingRecord.setTargetTableComment(targetTableComment);
            tableMappingRecord.setTargetDdlStatement(targetDdlStatement);
            tableMappingRecord.setSourceRecordCount(sourceRecordCount);
            tableMappingRecord.setTargetRecordCount(targetRecordCount);
            tableMappingRecord.setFieldCount(sourceColumns != null ? sourceColumns.size() : 0);

            // 设置操作人信息
            User currentUser = getCurrentUser();
            if (currentUser != null) {
                tableMappingRecord.setOperatorId(currentUser.getId());
                tableMappingRecord.setOperatorName(currentUser.getDisplayName());
            }

            // 插入表映射记录
            int result = tableMappingRecordMapper.insert(tableMappingRecord);
            if (result > 0) {
                // 添加字段映射记录
                if (sourceColumns != null && targetColumns != null) {
                    Map<String, String> fieldMappings = createFieldMappings(sourceColumns, targetColumns);
                    addFieldMappings(migrationBatchId, migrationRecord.getId(), tableMappingRecord.getId(), 
                                   sourceTableName, targetTableName, sourceColumns, targetColumns, fieldMappings);
                }
                
                logger.info("添加表映射记录: 迁移批次={}, 源表={}, 目标表={}, 字段数={}", 
                          migrationBatchId, sourceTableName, targetTableName, tableMappingRecord.getFieldCount());
                          
                return tableMappingRecord;
            } else {
                throw new RuntimeException("创建表映射记录失败");
            }
        } catch (Exception e) {
            logger.error("创建表映射记录失败: " + sourceTableName, e);
            throw new RuntimeException("创建表映射记录失败: " + e.getMessage());
        }
    }

    @Override
    public TableMappingRecord addTableMappingWithAiPrompt(Long migrationBatchId,
                                                          String sourceTableName, String sourceTableSchema, String sourceTableComment,
                                                          String targetTableName, String targetTableComment, String aiPromptDescription,
                                                          String targetDdlStatement,
                                                          List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns,
                                                          long sourceRecordCount, long targetRecordCount) {
        try {
            // 查找迁移记录
            MigrationRecord migrationRecord = migrationRecordMapper.selectByBatchId(migrationBatchId);
            if (migrationRecord == null) {
                throw new RuntimeException("迁移记录不存在: " + migrationBatchId);
            }

            // 创建表映射记录，包含冗余字段
            TableMappingRecord tableMappingRecord = new TableMappingRecord(
                migrationBatchId, migrationRecord.getId(), sourceTableName, targetTableName
            );
            tableMappingRecord.setSourceTableSchema(sourceTableSchema);
            tableMappingRecord.setSourceTableComment(sourceTableComment);
            tableMappingRecord.setTargetTableComment(targetTableComment);
            tableMappingRecord.setAiPromptDescription(aiPromptDescription);  // 设置AI提示词描述
            tableMappingRecord.setTargetDdlStatement(targetDdlStatement);
            tableMappingRecord.setSourceRecordCount(sourceRecordCount);
            tableMappingRecord.setTargetRecordCount(targetRecordCount);
            tableMappingRecord.setFieldCount(sourceColumns != null ? sourceColumns.size() : 0);

            // 设置操作人信息
            User currentUser = getCurrentUser();
            if (currentUser != null) {
                tableMappingRecord.setOperatorId(currentUser.getId());
                tableMappingRecord.setOperatorName(currentUser.getDisplayName());
            }

            // 插入表映射记录
            tableMappingRecordMapper.insert(tableMappingRecord);

            logger.info("创建表映射记录（含AI提示词）: ID={}, 源表={}, 目标表={}, AI提示词长度={}",
                      tableMappingRecord.getId(), sourceTableName, targetTableName,
                      aiPromptDescription != null ? aiPromptDescription.length() : 0);

            // 添加字段映射记录
            if (sourceColumns != null && targetColumns != null) {
                addFieldMappings(migrationBatchId, migrationRecord.getId(), tableMappingRecord.getId(),
                               sourceTableName, targetTableName, sourceColumns, targetColumns, null);
            }

            return tableMappingRecord;

        } catch (Exception e) {
            logger.error("创建表映射记录（含AI提示词）失败: " + sourceTableName, e);
            throw new RuntimeException("创建表映射记录失败: " + e.getMessage());
        }
    }

    @Override
    public void completeTableMapping(Long tableMappingId, boolean success, String errorMessage) {
        try {
            TableMappingRecord tableMappingRecord = tableMappingRecordMapper.selectById(tableMappingId);
            if (tableMappingRecord == null) {
                logger.warn("表映射记录不存在: {}", tableMappingId);
                return;
            }
            
            if (success) {
                tableMappingRecord.markAsCompleted();
                tableMappingRecord.setDataMigrationStatus("SUCCESS");
            } else {
                tableMappingRecord.markAsFailed(errorMessage);
                tableMappingRecord.setDataMigrationStatus("FAILED");
            }
            
            tableMappingRecordMapper.updateById(tableMappingRecord);
            
            logger.info("完成表映射: ID={}, 成功={}", 
                      tableMappingId, success);
                      
        } catch (Exception e) {
            logger.error("完成表映射记录失败: " + tableMappingId, e);
            throw new RuntimeException("完成表映射记录失败: " + e.getMessage());
        }
    }
    
    @Override
    public void addFieldMappings(Long tableMappingId, List<ColumnInfo> sourceColumns,
                               List<ColumnInfo> targetColumns, Map<String, String> fieldMappings) {
        // 为了兼容旧接口，这里需要获取冗余字段信息
        TableMappingRecord tableMappingRecord = tableMappingRecordMapper.selectById(tableMappingId);
        if (tableMappingRecord != null) {
            addFieldMappings(tableMappingRecord.getMigrationBatchId(), 
                           tableMappingRecord.getMigrationRecordId(), tableMappingId,
                           tableMappingRecord.getSourceTableName(), tableMappingRecord.getTargetTableName(),
                           sourceColumns, targetColumns, fieldMappings);
        }
    }
    
    /**
     * 添加字段映射记录（严格防止重复id字段）
     */
    public void addFieldMappings(Long migrationBatchId, Long migrationRecordId, Long tableMappingId, 
                               String sourceTableName, String targetTableName,
                               List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns, 
                               Map<String, String> fieldMappings) {
        try {
            List<FieldMappingRecord> fieldRecords = new ArrayList<>();
            logger.info("开始记录字段映射: 源字段数={}, 目标字段数={}, 映射关系数={}", 
                       sourceColumns.size(), targetColumns.size(), fieldMappings.size());
            // 检查是否为双主键设计
            boolean isDualPrimaryKey = targetColumns.stream()
                .anyMatch(col -> "id".equals(col.getColumnName()) && col.isPrimaryKey()) &&
                targetColumns.stream()
                .anyMatch(col -> "business_id".equals(col.getColumnName()));
            logger.info("检测到双主键设计: {}", isDualPrimaryKey);
            int position = 1;
            // 1. 系统ID字段（源字段为空，目标字段id）
            if (isDualPrimaryKey) {
                ColumnInfo systemIdColumn = targetColumns.stream()
                    .filter(col -> "id".equals(col.getColumnName()) && col.isPrimaryKey())
                    .findFirst().orElse(null);
                if (systemIdColumn != null) {
                    FieldMappingRecord systemIdRecord = createSystemFieldRecord(
                        migrationBatchId, migrationRecordId, tableMappingId,
                        sourceTableName, targetTableName, systemIdColumn, position++
                    );
                    fieldRecords.add(systemIdRecord);
                    logger.info("创建系统ID字段映射: 空源字段 -> {}", systemIdColumn.getColumnName());
                }
            }
            // 2. 源表主键字段只映射到business_id
            ColumnInfo businessIdColumn = targetColumns.stream()
                .filter(col -> "business_id".equals(col.getColumnName()))
                .findFirst().orElse(null);
            for (ColumnInfo sourceColumn : sourceColumns) {
                if (sourceColumn.isPrimaryKey() && businessIdColumn != null) {
                    FieldMappingRecord pkRecord = createFieldMappingRecord(
                        migrationBatchId, migrationRecordId, tableMappingId,
                        sourceTableName, targetTableName,
                        sourceColumn, businessIdColumn, position++
                    );
                    fieldRecords.add(pkRecord);
                    logger.info("主键字段映射: {} -> business_id", sourceColumn.getColumnName());
                    continue;
                }
            }
            // 3. 其他字段顺序映射，跳过目标表id字段和business_id字段
            List<String> skipFields = new ArrayList<>();
            skipFields.add("id");
            int targetIndex = 0;
            for (ColumnInfo targetColumn : targetColumns) {
                if (skipFields.contains(targetColumn.getColumnName())) {
                    continue;
                }
                // 找到下一个非主键的源字段
                ColumnInfo sourceColumn = null;
                while (targetIndex < sourceColumns.size()) {
                    ColumnInfo candidate = sourceColumns.get(targetIndex++);
                    if (!candidate.isPrimaryKey()) {
                        sourceColumn = candidate;
                        break;
                    }
                }
                if (sourceColumn != null) {
                    FieldMappingRecord record = createFieldMappingRecord(
                        migrationBatchId, migrationRecordId, tableMappingId,
                        sourceTableName, targetTableName,
                        sourceColumn, targetColumn, position++
                    );
                    fieldRecords.add(record);
                    logger.info("普通字段映射: {} -> {}", sourceColumn.getColumnName(), targetColumn.getColumnName());
                }
            }
            // 4. 批量插入
            if (!fieldRecords.isEmpty()) {
                fieldMappingRecordMapper.batchInsert(fieldRecords);
                logger.info("✅ 批量添加字段映射记录完成: 表映射ID={}, 成功记录数={}", 
                           tableMappingId, fieldRecords.size());
                logger.info("=== 字段映射详情 ===");
                for (FieldMappingRecord record : fieldRecords) {
                    logger.info("位置{}: {} -> {} (规则: {})", 
                               record.getFieldOrderPosition(),
                               record.getSourceFieldName(), 
                               record.getTargetFieldName(), 
                               record.getMappingRule());
                }
                logger.info("=== 映射详情结束 ===");
            } else {
                logger.warn("❌ 没有字段映射记录被创建");
            }
        } catch (Exception e) {
            logger.error("添加字段映射记录失败: " + tableMappingId, e);
            throw new RuntimeException("添加字段映射记录失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public MigrationRecord getMigrationRecordWithDetails(Long migrationBatchId) {
        // 查询迁移记录
        MigrationRecord migrationRecord = migrationRecordMapper.selectByBatchId(migrationBatchId);
        if (migrationRecord == null) {
            return null;
        }
        
        // 查询关联的表映射记录（使用批次ID查询）
        List<TableMappingRecord> tableMappingRecords = tableMappingRecordMapper.selectByMigrationBatchId(migrationRecord.getMigrationBatchId());
        
        // 为每个表映射记录查询字段映射记录
        for (TableMappingRecord tableMappingRecord : tableMappingRecords) {
            logger.debug("查询表映射记录ID {} 的字段映射记录", tableMappingRecord.getId());
            List<FieldMappingRecord> fieldMappingRecords = fieldMappingRecordMapper.selectByTableMappingRecordId(tableMappingRecord.getId());
            logger.debug("表映射记录ID {} 查询到 {} 个字段映射记录", tableMappingRecord.getId(), fieldMappingRecords.size());
            tableMappingRecord.setFieldMappingRecords(fieldMappingRecords);
        }
        
        // 设置表映射记录到迁移记录中
        migrationRecord.setTableMappingRecords(tableMappingRecords);
        
        logger.debug("加载迁移记录详情: 批次ID={}, 表映射数={}, 总字段映射数={}", 
                    migrationBatchId, tableMappingRecords.size(),
                    tableMappingRecords.stream().mapToInt(t -> t.getFieldMappingRecords() != null ? t.getFieldMappingRecords().size() : 0).sum());
        
        return migrationRecord;
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MigrationRecord> getMigrationRecords(int pageNum, int pageSize) {
        int offset = (pageNum - 1) * pageSize;
        return migrationRecordMapper.selectAll(offset, pageSize);
    }
    
    @Override
    @Transactional(readOnly = true)
    public int getMigrationRecordCount() {
        return migrationRecordMapper.selectCount();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MigrationRecord> getMigrationRecordsByConfigId(Long configId) {
        List<MigrationRecord> sourceRecords = migrationRecordMapper.selectBySourceConfigId(configId);
        List<MigrationRecord> targetRecords = migrationRecordMapper.selectByTargetConfigId(configId);
        
        Set<Long> recordIds = new HashSet<>();
        List<MigrationRecord> allRecords = new ArrayList<>();
        
        for (MigrationRecord record : sourceRecords) {
            if (recordIds.add(record.getId())) {
                allRecords.add(record);
            }
        }
        
        for (MigrationRecord record : targetRecords) {
            if (recordIds.add(record.getId())) {
                allRecords.add(record);
            }
        }
        
        return allRecords;
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MigrationRecord> getRecentMigrationRecords(int limit) {
        return migrationRecordMapper.selectRecent(limit);
    }
    
    @Override
    @Transactional(readOnly = true)
    public MigrationStatistics getMigrationStatistics() {
        return migrationRecordMapper.getMigrationStatistics();
    }
    
    @Override
    @Transactional(readOnly = true)
    public TableMappingStatistics getTableMappingStatistics(Long migrationRecordId) {
        return tableMappingRecordMapper.getTableMappingStatistics(migrationRecordId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public FieldMappingStatistics getFieldMappingStatistics(Long tableMappingRecordId) {
        return fieldMappingRecordMapper.getFieldMappingStatistics(tableMappingRecordId);
    }
    
    @Override
    public void deleteMigrationRecord(Long migrationRecordId) {
        deleteMigrationRecordWithCascade(migrationRecordId);
    }

    @Override
    @Transactional
    public String deleteMigrationRecordWithCascade(Long migrationRecordId) {
        try {
            // 1. 查询迁移记录信息
            MigrationRecord migrationRecord = migrationRecordMapper.selectById(migrationRecordId);
            if (migrationRecord == null) {
                return "迁移记录不存在，无需删除";
            }

            String sourceTableName = migrationRecord.getSourceTableName();
            String targetTableName = migrationRecord.getTargetTableName();
            Long migrationBatchId = migrationRecord.getMigrationBatchId();

            logger.info("🗑️ 开始删除迁移记录: ID={}, 源表={}, 目标表={}, 批次ID={}",
                       migrationRecordId, sourceTableName, targetTableName, migrationBatchId);

            // 2. 删除字段映射记录
            int deletedFieldMappings = fieldMappingRecordMapper.deleteByMigrationRecordId(migrationRecordId);
            logger.info("🗑️ 删除字段映射记录: {} 条", deletedFieldMappings);

            // 3. 删除表映射记录
            int deletedTableMappings = tableMappingRecordMapper.deleteByMigrationRecordId(migrationRecordId);
            logger.info("🗑️ 删除表映射记录: {} 条", deletedTableMappings);

            // 4. 删除迁移记录
            int deletedMigrationRecord = migrationRecordMapper.deleteById(migrationRecordId);
            logger.info("🗑️ 删除迁移记录: {} 条", deletedMigrationRecord);

            // 5. 如果是批次中的最后一个记录，也删除批次相关的其他记录
            if (migrationBatchId != null) {
                List<MigrationRecord> remainingRecords = migrationRecordMapper.selectByMigrationBatchId(migrationBatchId);
                if (remainingRecords.isEmpty()) {
                    // 删除批次相关的所有残留记录
                    int deletedBatchFieldMappings = fieldMappingRecordMapper.deleteByMigrationBatchId(migrationBatchId);
                    int deletedBatchTableMappings = tableMappingRecordMapper.deleteByMigrationBatchId(migrationBatchId);
                    logger.info("🗑️ 清理批次残留记录: 字段映射={} 条, 表映射={} 条",
                               deletedBatchFieldMappings, deletedBatchTableMappings);
                }
            }

            String result = String.format("✅ 成功删除迁移记录及关联数据: 迁移记录 %d 条, 表映射记录 %d 条, 字段映射记录 %d 条",
                                         deletedMigrationRecord, deletedTableMappings, deletedFieldMappings);

            logger.info("🗑️ {}", result);
            return result;

        } catch (Exception e) {
            logger.error("❌ 删除迁移记录失败: migrationRecordId={}", migrationRecordId, e);
            throw new RuntimeException("删除迁移记录失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<TableMappingRecord> getTableMigrationHistory(String sourceTableName, String targetTableName) {
        // 使用迁移记录中的source_table_name进行匹配查询
        List<MigrationRecord> matchingMigrationRecords = migrationRecordMapper.selectBySourceTableName(sourceTableName);
        
        List<TableMappingRecord> result = new ArrayList<>();
        
        for (MigrationRecord migrationRecord : matchingMigrationRecords) {
            // 查询该迁移记录的表映射记录
            List<TableMappingRecord> tableMappings = tableMappingRecordMapper.selectByMigrationRecordId(migrationRecord.getId());
            
            for (TableMappingRecord tableMapping : tableMappings) {
                // 如果指定了目标表名，则进行过滤
                if (targetTableName != null && !targetTableName.equals(tableMapping.getTargetTableName())) {
                    continue;
                }
                
                // 查询字段映射记录以进行完整的字段结构对比
                List<FieldMappingRecord> fieldMappings = fieldMappingRecordMapper.selectByTableMappingRecordId(tableMapping.getId());
                tableMapping.setFieldMappingRecords(fieldMappings);
                
                result.add(tableMapping);
            }
        }
        
        // 按迁移时间倒序排列
        result.sort((a, b) -> {
            if (a.getMigrationStartTime() == null && b.getMigrationStartTime() == null) return 0;
            if (a.getMigrationStartTime() == null) return 1;
            if (b.getMigrationStartTime() == null) return -1;
            return b.getMigrationStartTime().compareTo(a.getMigrationStartTime());
        });
        
        return result;
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<FieldMappingRecord> getFieldMappingHistory(String sourceFieldName, String targetFieldName) {
        return fieldMappingRecordMapper.selectBySourceFieldName(sourceFieldName);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MigrationRecord> getMigrationRecordsBySourceTable(String sourceTableName) {
        return migrationRecordMapper.selectBySourceTableName(sourceTableName);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MigrationRecord> getMigrationRecordsByTargetTable(String targetTableName) {
        return migrationRecordMapper.selectByTargetTableName(targetTableName);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<MigrationRecord> getMigrationRecordsByTableMapping(String sourceTableName, String targetTableName) {
        return migrationRecordMapper.selectByTableMapping(sourceTableName, targetTableName);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean isTableMigrationExists(String sourceTableName, String targetTableName) {
        return migrationRecordMapper.checkTableMigrationExists(sourceTableName, targetTableName) > 0;
    }
    
    @Override
    @Transactional
    public int deleteAllHistoryBySourceTable(String sourceTableName) {
        logger.info("开始删除源表 {} 的所有历史迁移记录", sourceTableName);
        
        // 1. 查询该源表的所有迁移记录
        List<MigrationRecord> migrationRecords = migrationRecordMapper.selectBySourceTableName(sourceTableName);
        
        if (migrationRecords.isEmpty()) {
            logger.info("源表 {} 没有历史迁移记录", sourceTableName);
            return 0;
        }
        
        int deletedCount = 0;
        
        for (MigrationRecord migrationRecord : migrationRecords) {
            Long migrationRecordId = migrationRecord.getId();
            Long migrationBatchId = migrationRecord.getMigrationBatchId();
            
            logger.info("删除迁移记录: ID={}, BatchId={}, 源表={}, 目标表={}", 
                       migrationRecordId, migrationBatchId, 
                       migrationRecord.getSourceTableName(), migrationRecord.getTargetTableName());
            
            // 2. 删除字段映射记录
            int deletedFieldMappings = fieldMappingRecordMapper.deleteByMigrationRecordId(migrationRecordId);
            logger.debug("删除字段映射记录 {} 条", deletedFieldMappings);
            
            // 3. 删除表映射记录
            int deletedTableMappings = tableMappingRecordMapper.deleteByMigrationRecordId(migrationRecordId);
            logger.debug("删除表映射记录 {} 条", deletedTableMappings);
            
            // 4. 删除迁移记录
            int deletedMigrationRecord = migrationRecordMapper.deleteById(migrationRecordId);
            logger.debug("删除迁移记录 {} 条", deletedMigrationRecord);
            
            deletedCount += deletedMigrationRecord;
        }
        
        logger.info("源表 {} 的历史迁移记录删除完成，共删除 {} 条迁移记录", sourceTableName, deletedCount);
        return deletedCount;
    }
    
    @Override
    public Long recordTableMigration(Long sourceConfigId, Long targetConfigId,
                                     String sourceDatabaseName, String targetDatabaseName,
                                     String sourceTableName, String targetTableName,
                                     String targetDdlStatement,
                                     List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns,
                                     long sourceRecordCount, long targetRecordCount,
                                     boolean migrationSuccess, String errorMessage) {
        try {
            // 先删除该源表的所有历史迁移记录
            int deletedHistoryCount = deleteAllHistoryBySourceTable(sourceTableName);
            if (deletedHistoryCount > 0) {
                logger.info("删除源表 {} 的 {} 条历史迁移记录后，开始新的迁移记录", sourceTableName, deletedHistoryCount);
            }
            
            // 开始迁移记录 - 修改notes格式，不再包含表名
            MigrationRecord migrationRecord = startMigration(sourceConfigId, targetConfigId, 
                                                           sourceDatabaseName, targetDatabaseName, 
                                                           "单表迁移操作");
            Long migrationBatchId = migrationRecord.getMigrationBatchId();
            
            // 设置源表名和目标表名
            migrationRecord.setSourceTableName(sourceTableName);
            migrationRecord.setTargetTableName(targetTableName);
            
            // 更新迁移记录以包含表名信息
            migrationRecordMapper.updateById(migrationRecord);
            
            // 添加表映射记录
            TableMappingRecord tableMappingRecord = addTableMapping(migrationBatchId,
                                                                   sourceTableName, null, null,
                                                                   targetTableName, null, targetDdlStatement,
                                                                   sourceColumns, targetColumns,
                                                                   sourceRecordCount, targetRecordCount);
            
            // 完成表映射
            completeTableMapping(tableMappingRecord.getId(), migrationSuccess, errorMessage);
            
            // 完成迁移
            completeMigration(migrationBatchId, migrationSuccess, errorMessage);
            
            logger.info("记录表迁移完成: 批次ID={}, 源表={}, 目标表={}, 成功={}", 
                       migrationBatchId, sourceTableName, targetTableName, migrationSuccess);
            
            return migrationBatchId;
            
        } catch (Exception e) {
            logger.error("记录表迁移失败: " + sourceTableName, e);
            throw new RuntimeException("记录表迁移失败: " + e.getMessage());
        }
    }

    @Override
    public Long recordTableMigrationWithAiPrompt(Long sourceConfigId, Long targetConfigId,
                                                 String sourceDatabaseName, String targetDatabaseName,
                                                 String sourceTableName, String targetTableName,
                                                 String targetDdlStatement, String aiPromptDescription,
                                                 List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns,
                                                 long sourceRecordCount, long targetRecordCount,
                                                 boolean migrationSuccess, String errorMessage) {
        try {
            // 先删除该源表的所有历史迁移记录
            int deletedHistoryCount = deleteAllHistoryBySourceTable(sourceTableName);
            if (deletedHistoryCount > 0) {
                logger.info("删除源表 {} 的 {} 条历史迁移记录后，开始新的迁移记录", sourceTableName, deletedHistoryCount);
            }

            // 开始迁移记录 - 修改notes格式，不再包含表名
            MigrationRecord migrationRecord = startMigration(sourceConfigId, targetConfigId,
                                                           sourceDatabaseName, targetDatabaseName,
                                                           "单表迁移操作（含AI优化）");
            Long migrationBatchId = migrationRecord.getMigrationBatchId();

            // 设置源表名和目标表名
            migrationRecord.setSourceTableName(sourceTableName);
            migrationRecord.setTargetTableName(targetTableName);

            // 更新迁移记录以包含表名信息
            migrationRecordMapper.updateById(migrationRecord);

            // 添加表映射记录（包含AI提示词）
            TableMappingRecord tableMappingRecord = addTableMappingWithAiPrompt(migrationBatchId,
                                                                               sourceTableName, null, null,
                                                                               targetTableName, null, aiPromptDescription,
                                                                               targetDdlStatement,
                                                                               sourceColumns, targetColumns,
                                                                               sourceRecordCount, targetRecordCount);

            // 完成表映射
            completeTableMapping(tableMappingRecord.getId(), migrationSuccess, errorMessage);

            // 完成迁移
            completeMigration(migrationBatchId, migrationSuccess, errorMessage);

            logger.info("记录表迁移完成（含AI提示词）: 批次ID={}, 源表={}, 目标表={}, 成功={}, AI提示词长度={}",
                       migrationBatchId, sourceTableName, targetTableName, migrationSuccess,
                       aiPromptDescription != null ? aiPromptDescription.length() : 0);

            return migrationBatchId;

        } catch (Exception e) {
            logger.error("记录表迁移失败（含AI提示词）: " + sourceTableName, e);
            throw new RuntimeException("记录表迁移失败: " + e.getMessage());
        }
    }

    /**
     * 创建字段映射关系（支持双主键设计）
     */
    private Map<String, String> createFieldMappings(List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns) {
        Map<String, String> fieldMappings = new HashMap<>();
        
        // 检查是否为双主键设计
        boolean isDualPrimaryKey = targetColumns.stream()
            .anyMatch(col -> "id".equals(col.getColumnName()) && col.isPrimaryKey()) &&
            targetColumns.stream()
            .anyMatch(col -> "business_id".equals(col.getColumnName()));
            
        // 如果是双主键设计，需要忽略系统生成的id字段
        List<ColumnInfo> effectiveTargetColumns = isDualPrimaryKey ?
            targetColumns.stream()
                .filter(col -> !("id".equals(col.getColumnName()) && col.isPrimaryKey()))
                .collect(Collectors.toList()) :
            targetColumns;
            
        // 遍历源字段，尝试找到对应的目标字段
        for (ColumnInfo sourceColumn : sourceColumns) {
            String sourceFieldName = sourceColumn.getColumnName();
            
            // 1. 尝试直接匹配
            Optional<ColumnInfo> directMatch = effectiveTargetColumns.stream()
                .filter(targetColumn -> targetColumn.getColumnName().equals(sourceFieldName))
                .findFirst();
                
            if (directMatch.isPresent()) {
                fieldMappings.put(sourceFieldName, sourceFieldName);
                continue;
            }
            
            // 2. 尝试匹配转换后的名称（例如：user_name -> userName）
            String camelCaseFieldName = toCamelCase(sourceFieldName);
            Optional<ColumnInfo> camelCaseMatch = effectiveTargetColumns.stream()
                .filter(targetColumn -> targetColumn.getColumnName().equals(camelCaseFieldName))
                .findFirst();
                
            if (camelCaseMatch.isPresent()) {
                fieldMappings.put(sourceFieldName, camelCaseFieldName);
                continue;
            }
            
            // 3. 尝试匹配下划线命名（例如：userName -> user_name）
            String snakeCaseFieldName = toSnakeCase(sourceFieldName);
            Optional<ColumnInfo> snakeCaseMatch = effectiveTargetColumns.stream()
                .filter(targetColumn -> targetColumn.getColumnName().equals(snakeCaseFieldName))
                .findFirst();
                
            if (snakeCaseMatch.isPresent()) {
                fieldMappings.put(sourceFieldName, snakeCaseFieldName);
            }
        }
        
        return fieldMappings;
    }
    
    /**
     * 转换为驼峰命名
     */
    private String toCamelCase(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        
        StringBuilder result = new StringBuilder();
        boolean nextUpper = false;
        
        for (int i = 0; i < str.length(); i++) {
            char currentChar = str.charAt(i);
            
            if (currentChar == '_') {
                nextUpper = true;
            } else {
                if (nextUpper) {
                    result.append(Character.toUpperCase(currentChar));
                    nextUpper = false;
                } else {
                    result.append(Character.toLowerCase(currentChar));
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * 转换为下划线命名
     */
    private String toSnakeCase(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        
        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(str.charAt(0)));
        
        for (int i = 1; i < str.length(); i++) {
            char currentChar = str.charAt(i);
            if (Character.isUpperCase(currentChar)) {
                result.append('_');
                result.append(Character.toLowerCase(currentChar));
            } else {
                result.append(currentChar);
            }
        }
        
        return result.toString();
    }
    
    /**
     * 创建字段映射记录（包含冗余字段）
     */
    private FieldMappingRecord createFieldMappingRecord(Long migrationBatchId, Long migrationRecordId, Long tableMappingId,
                                                      String sourceTableName, String targetTableName,
                                                      ColumnInfo sourceColumn, ColumnInfo targetColumn, int position) {
        FieldMappingRecord fieldRecord = new FieldMappingRecord(
            migrationBatchId, migrationRecordId, tableMappingId,
            sourceTableName, targetTableName,
            sourceColumn.getColumnName(), targetColumn.getColumnName()
        );
        
        // 设置源字段信息
        fieldRecord.setSourceFieldType(sourceColumn.getDataType());
        fieldRecord.setSourceFieldComment(sourceColumn.getComment());
        fieldRecord.setSourceFieldNullable(sourceColumn.isNullable());
        fieldRecord.setSourceFieldDefaultValue(sourceColumn.getDefaultValue());
        fieldRecord.setSourceFieldIsPrimary(sourceColumn.isPrimaryKey());
        
        // 设置目标字段信息
        fieldRecord.setTargetFieldType(targetColumn.getDataType());
        fieldRecord.setTargetFieldComment(targetColumn.getComment());
        fieldRecord.setTargetFieldNullable(targetColumn.isNullable());
        fieldRecord.setTargetFieldDefaultValue(targetColumn.getDefaultValue());
        fieldRecord.setTargetFieldIsPrimary(targetColumn.isPrimaryKey());
        
        // 设置位置和映射规则
        fieldRecord.setFieldOrderPosition(position);

        // 设置操作人信息
        User currentUser = getCurrentUser();
        if (currentUser != null) {
            fieldRecord.setOperatorId(currentUser.getId());
            fieldRecord.setOperatorName(currentUser.getDisplayName());
        }

        // 判断映射规则
        if (sourceColumn.getColumnName().equals(targetColumn.getColumnName())) {
            if (sourceColumn.getDataType().equals(targetColumn.getDataType())) {
                fieldRecord.setMappingRule("DIRECT");
            } else {
                fieldRecord.setMappingRule("TYPE_CONVERT");
                fieldRecord.setConversionNotes("类型转换: " + sourceColumn.getDataType() + " -> " + targetColumn.getDataType());
            }
        } else {
            fieldRecord.setMappingRule("NAME_CONVERT");
            fieldRecord.setConversionNotes("字段名转换: " + sourceColumn.getColumnName() + " -> " + targetColumn.getColumnName());
        }
        
        return fieldRecord;
    }
    
    /**
     * 创建未映射字段记录（只有源字段信息）
     */
    private FieldMappingRecord createUnmappedFieldRecord(Long migrationBatchId, Long migrationRecordId, Long tableMappingId,
                                                       String sourceTableName, String targetTableName,
                                                       ColumnInfo sourceColumn, int position) {
        FieldMappingRecord fieldRecord = new FieldMappingRecord(
            migrationBatchId, migrationRecordId, tableMappingId,
            sourceTableName, targetTableName,
            sourceColumn.getColumnName(), null // 目标字段名为null
        );
        
        // 设置源字段信息
        fieldRecord.setSourceFieldType(sourceColumn.getDataType());
        fieldRecord.setSourceFieldComment(sourceColumn.getComment());
        fieldRecord.setSourceFieldNullable(sourceColumn.isNullable());
        fieldRecord.setSourceFieldDefaultValue(sourceColumn.getDefaultValue());
        fieldRecord.setSourceFieldIsPrimary(sourceColumn.isPrimaryKey());
        
        // 目标字段信息设为null或默认值
        fieldRecord.setTargetFieldType(null);
        fieldRecord.setTargetFieldComment("字段未映射");
        fieldRecord.setTargetFieldNullable(null);
        fieldRecord.setTargetFieldDefaultValue(null);
        fieldRecord.setTargetFieldIsPrimary(false);
        
        // 设置位置和映射规则
        fieldRecord.setFieldOrderPosition(position);
        fieldRecord.setMappingRule("UNMAPPED");
        fieldRecord.setConversionNotes("源字段未找到对应的目标字段");
        
        return fieldRecord;
    }
    
    /**
     * 创建系统字段记录（双主键设计中的系统ID字段）
     */
    private FieldMappingRecord createSystemFieldRecord(Long migrationBatchId, Long migrationRecordId, Long tableMappingId,
                                                      String sourceTableName, String targetTableName,
                                                      ColumnInfo systemColumn, int position) {
        FieldMappingRecord fieldRecord = new FieldMappingRecord(
            migrationBatchId, migrationRecordId, tableMappingId,
            sourceTableName, targetTableName,
            "__SYSTEM_GENERATED__", // 使用特殊标识符而不是null
            systemColumn.getColumnName()
        );
        
        // 源字段信息使用特殊标识符（表示系统生成）
        fieldRecord.setSourceFieldType("SYSTEM");
        fieldRecord.setSourceFieldComment("系统生成字段");
        fieldRecord.setSourceFieldNullable(false); // 使用false而不是null
        fieldRecord.setSourceFieldDefaultValue("");
        fieldRecord.setSourceFieldIsPrimary(false);
        
        // 设置目标字段信息
        fieldRecord.setTargetFieldType(systemColumn.getDataType());
        fieldRecord.setTargetFieldComment(systemColumn.getComment());
        fieldRecord.setTargetFieldNullable(systemColumn.isNullable());
        fieldRecord.setTargetFieldDefaultValue(systemColumn.getDefaultValue());
        fieldRecord.setTargetFieldIsPrimary(systemColumn.isPrimaryKey());
        
        // 设置位置和映射规则
        fieldRecord.setFieldOrderPosition(position);
        fieldRecord.setMappingRule("SYSTEM_GENERATED");
        fieldRecord.setConversionNotes("系统生成的主键字段（雪花算法）");
        
        return fieldRecord;
    }

    /**
     * 获取当前用户信息
     * 从Sa-Token Session中获取用户信息
     */
    private User getCurrentUser() {
        try {
            if (StpUtil.isLogin()) {
                Object sessionUser = StpUtil.getSession().get("user");
                if (sessionUser instanceof User) {
                    return (User) sessionUser;
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回默认用户
        }

        // 如果获取不到，返回默认用户信息
        return User.builder()
                .id(1L)
                .username("system")
                .realName("系统用户")
                .build();
    }
}