package com.liuyang.mapper;

import com.liuyang.entity.TableMappingRecord;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 表映射记录Mapper接口
 */
@Mapper
public interface TableMappingRecordMapper {
    
    /**
     * 插入表映射记录
     */
    @Insert("""
            INSERT INTO table_mapping_records (
                migration_batch_id, migration_record_id, source_table_name, source_table_schema,
                source_table_comment, source_table_type, target_table_name,
                target_table_comment, ai_prompt_description, target_ddl_statement, table_mapping_status,
                source_record_count, target_record_count, data_migration_status,
                field_count, error_message, migration_start_time,
                migration_end_time, migration_duration_ms
            ) VALUES (
                #{migrationBatchId}, #{migrationRecordId}, #{sourceTableName}, #{sourceTableSchema},
                #{sourceTableComment}, #{sourceTableType}, #{targetTableName},
                #{targetTableComment}, #{aiPromptDescription}, #{targetDdlStatement}, #{tableMappingStatus},
                #{sourceRecordCount}, #{targetRecordCount}, #{dataMigrationStatus},
                #{fieldCount}, #{errorMessage}, #{migrationStartTime},
                #{migrationEndTime}, #{migrationDurationMs}
            )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TableMappingRecord tableMappingRecord);
    
    /**
     * 根据ID更新表映射记录
     */
    @Update("""
            UPDATE table_mapping_records SET
                target_table_comment = #{targetTableComment},
                target_ddl_statement = #{targetDdlStatement},
                table_mapping_status = #{tableMappingStatus},
                source_record_count = #{sourceRecordCount},
                target_record_count = #{targetRecordCount},
                data_migration_status = #{dataMigrationStatus},
                field_count = #{fieldCount},
                error_message = #{errorMessage},
                migration_end_time = #{migrationEndTime},
                migration_duration_ms = #{migrationDurationMs}
            WHERE id = #{id}
            """)
    int updateById(TableMappingRecord tableMappingRecord);
    
    /**
     * 根据ID查询表映射记录
     */
    @Select("SELECT * FROM table_mapping_records WHERE id = #{id}")
    TableMappingRecord selectById(Long id);
    
    /**
     * 根据迁移批次ID查询表映射记录
     */
    @Select("""
            SELECT * FROM table_mapping_records 
            WHERE migration_batch_id = #{migrationBatchId}
            ORDER BY created_time ASC
            """)
    List<TableMappingRecord> selectByMigrationBatchId(Long migrationBatchId);
    
    /**
     * 根据迁移记录ID查询表映射记录
     */
    @Select("""
            SELECT * FROM table_mapping_records 
            WHERE migration_record_id = #{migrationRecordId}
            ORDER BY created_time ASC
            """)
    List<TableMappingRecord> selectByMigrationRecordId(Long migrationRecordId);
    
    /**
     * 根据源表名查询表映射记录
     */
    @Select("""
            SELECT * FROM table_mapping_records 
            WHERE source_table_name = #{sourceTableName}
            ORDER BY created_time DESC
            """)
    List<TableMappingRecord> selectBySourceTableName(String sourceTableName);
    
    /**
     * 根据目标表名查询表映射记录
     */
    @Select("""
            SELECT * FROM table_mapping_records 
            WHERE target_table_name = #{targetTableName}
            ORDER BY created_time DESC
            """)
    List<TableMappingRecord> selectByTargetTableName(String targetTableName);
    
    /**
     * 根据映射状态查询表映射记录
     */
    @Select("""
            SELECT * FROM table_mapping_records 
            WHERE table_mapping_status = #{status}
            ORDER BY created_time DESC
            """)
    List<TableMappingRecord> selectByStatus(String status);
    
    /**
     * 查询指定迁移记录下的成功表映射记录
     */
    @Select("""
            SELECT * FROM table_mapping_records 
            WHERE migration_record_id = #{migrationRecordId} 
            AND table_mapping_status = 'SUCCESS'
            ORDER BY created_time ASC
            """)
    List<TableMappingRecord> selectSuccessfulByMigrationRecordId(Long migrationRecordId);
    
    /**
     * 查询指定迁移记录下的失败表映射记录
     */
    @Select("""
            SELECT * FROM table_mapping_records 
            WHERE migration_record_id = #{migrationRecordId} 
            AND table_mapping_status = 'FAILED'
            ORDER BY created_time ASC
            """)
    List<TableMappingRecord> selectFailedByMigrationRecordId(Long migrationRecordId);
    
    /**
     * 统计指定迁移记录的表映射统计信息
     */
    @Select("""
            SELECT 
                COUNT(*) as total_tables,
                SUM(CASE WHEN table_mapping_status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_tables,
                SUM(CASE WHEN table_mapping_status = 'FAILED' THEN 1 ELSE 0 END) as failed_tables,
                SUM(source_record_count) as total_source_records,
                SUM(target_record_count) as total_target_records,
                SUM(field_count) as total_fields,
                AVG(migration_duration_ms) as avg_table_migration_duration
            FROM table_mapping_records 
            WHERE migration_record_id = #{migrationRecordId}
            """)
    @Results({
        @Result(property = "totalTables", column = "total_tables"),
        @Result(property = "successfulTables", column = "successful_tables"),
        @Result(property = "failedTables", column = "failed_tables"),
        @Result(property = "totalSourceRecords", column = "total_source_records"),
        @Result(property = "totalTargetRecords", column = "total_target_records"),
        @Result(property = "totalFields", column = "total_fields"),
        @Result(property = "avgTableMigrationDuration", column = "avg_table_migration_duration")
    })
    TableMappingStatistics getTableMappingStatistics(Long migrationRecordId);
    
    /**
     * 批量插入表映射记录
     */
    @Insert({
        "<script>",
        "INSERT INTO table_mapping_records (",
        "migration_batch_id, migration_record_id, source_table_name, source_table_schema,",
        "source_table_comment, source_table_type, target_table_name,",
        "target_table_comment, target_ddl_statement, table_mapping_status,",
        "source_record_count, target_record_count, data_migration_status,",
        "field_count, migration_start_time",
        ") VALUES ",
        "<foreach collection='list' item='item' separator=','>",
        "(#{item.migrationBatchId}, #{item.migrationRecordId}, #{item.sourceTableName}, #{item.sourceTableSchema},",
        "#{item.sourceTableComment}, #{item.sourceTableType}, #{item.targetTableName},",
        "#{item.targetTableComment}, #{item.targetDdlStatement}, #{item.tableMappingStatus},",
        "#{item.sourceRecordCount}, #{item.targetRecordCount}, #{item.dataMigrationStatus},",
        "#{item.fieldCount}, #{item.migrationStartTime})",
        "</foreach>",
        "</script>"
    })
    int batchInsert(List<TableMappingRecord> tableMappingRecords);
    
    /**
     * 删除表映射记录
     */
    @Delete("DELETE FROM table_mapping_records WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 根据迁移记录ID删除所有表映射记录
     */
    @Delete("DELETE FROM table_mapping_records WHERE migration_record_id = #{migrationRecordId}")
    int deleteByMigrationRecordId(Long migrationRecordId);
    
    /**
     * 根据迁移批次ID删除所有表映射记录
     */
    @Delete("DELETE FROM table_mapping_records WHERE migration_batch_id = #{migrationBatchId}")
    int deleteByMigrationBatchId(Long migrationBatchId);
    
    /**
     * 表映射统计信息DTO
     */
    class TableMappingStatistics {
        private Long totalTables;
        private Long successfulTables;
        private Long failedTables;
        private Long totalSourceRecords;
        private Long totalTargetRecords;
        private Long totalFields;
        private Double avgTableMigrationDuration;
        
        // Getter和Setter方法
        public Long getTotalTables() { return totalTables; }
        public void setTotalTables(Long totalTables) { this.totalTables = totalTables; }
        
        public Long getSuccessfulTables() { return successfulTables; }
        public void setSuccessfulTables(Long successfulTables) { this.successfulTables = successfulTables; }
        
        public Long getFailedTables() { return failedTables; }
        public void setFailedTables(Long failedTables) { this.failedTables = failedTables; }
        
        public Long getTotalSourceRecords() { return totalSourceRecords; }
        public void setTotalSourceRecords(Long totalSourceRecords) { this.totalSourceRecords = totalSourceRecords; }
        
        public Long getTotalTargetRecords() { return totalTargetRecords; }
        public void setTotalTargetRecords(Long totalTargetRecords) { this.totalTargetRecords = totalTargetRecords; }
        
        public Long getTotalFields() { return totalFields; }
        public void setTotalFields(Long totalFields) { this.totalFields = totalFields; }
        
        public Double getAvgTableMigrationDuration() { return avgTableMigrationDuration; }
        public void setAvgTableMigrationDuration(Double avgTableMigrationDuration) { this.avgTableMigrationDuration = avgTableMigrationDuration; }
    }


}