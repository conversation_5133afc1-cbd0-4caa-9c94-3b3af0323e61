#!/bin/bash

# AI提示词功能测试脚本
echo "🧪 AI提示词功能测试..."

# 设置数据库变量
DB_HOST="rm-2zebik7o7a04ddnvowo.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_NAME="db_config_management"
DB_USER="healthcar_test"
DB_PASS="l\$!d#!C07LVj%b"

echo "📊 测试环境信息:"
echo "  数据库: $DB_HOST:$DB_PORT/$DB_NAME"
echo "  用户: $DB_USER"

# 1. 检查数据库连接
echo ""
echo "🔍 1. 检查数据库连接..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1 as connection_test;" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败"
    exit 1
fi

# 2. 检查ai_prompt_description字段
echo ""
echo "🔍 2. 检查ai_prompt_description字段..."
FIELD_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SHOW COLUMNS FROM table_mapping_records LIKE 'ai_prompt_description';" | wc -l)

if [ $FIELD_EXISTS -gt 1 ]; then
    echo "✅ ai_prompt_description字段存在"
    
    # 显示字段详情
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
        SELECT 
            COLUMN_NAME as '字段名',
            DATA_TYPE as '数据类型',
            IS_NULLABLE as '允许NULL',
            COLUMN_COMMENT as '字段注释'
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'table_mapping_records' 
            AND COLUMN_NAME = 'ai_prompt_description'
            AND TABLE_SCHEMA = '$DB_NAME';
    "
else
    echo "❌ ai_prompt_description字段不存在，请先执行迁移脚本"
    echo "   运行: ./scripts/apply-ai-prompt-migration.sh"
    exit 1
fi

# 3. 检查最近的迁移记录
echo ""
echo "🔍 3. 检查最近的迁移记录..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
    SELECT 
        id,
        source_table_name as '源表名',
        target_table_name as '目标表名',
        CASE 
            WHEN ai_prompt_description IS NULL THEN '无'
            WHEN LENGTH(ai_prompt_description) = 0 THEN '空'
            ELSE CONCAT(LEFT(ai_prompt_description, 50), '...')
        END as 'AI提示词(前50字符)',
        LENGTH(ai_prompt_description) as '提示词长度',
        created_time as '创建时间'
    FROM table_mapping_records 
    ORDER BY created_time DESC 
    LIMIT 5;
"

# 4. 统计AI提示词使用情况
echo ""
echo "📈 4. AI提示词使用统计..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
    SELECT 
        COUNT(*) as '总记录数',
        SUM(CASE WHEN ai_prompt_description IS NOT NULL AND LENGTH(ai_prompt_description) > 0 THEN 1 ELSE 0 END) as '有AI提示词',
        SUM(CASE WHEN ai_prompt_description IS NULL OR LENGTH(ai_prompt_description) = 0 THEN 1 ELSE 0 END) as '无AI提示词',
        ROUND(AVG(LENGTH(ai_prompt_description)), 2) as '平均提示词长度'
    FROM table_mapping_records;
"

# 5. 检查应用是否运行
echo ""
echo "🔍 5. 检查应用状态..."
if curl -f http://localhost:9999/actuator/health &>/dev/null; then
    echo "✅ 应用正在运行"
    
    # 检查健康状态详情
    echo "📊 应用健康状态:"
    curl -s http://localhost:9999/actuator/health | jq . 2>/dev/null || curl -s http://localhost:9999/actuator/health
else
    echo "⚠️ 应用未运行或无法访问"
    echo "   请启动应用: ./scripts/start-prod.sh"
fi

# 6. 测试建议
echo ""
echo "🧪 测试建议:"
echo "  1. 启动应用后，进行一次数据迁移操作"
echo "  2. 在迁移时提供业务描述，触发AI表名优化"
echo "  3. 检查迁移记录中是否保存了AI提示词"
echo "  4. 验证MySQL表是否包含了正确的COMMENT"

# 7. 示例测试SQL
echo ""
echo "📝 手动测试SQL示例:"
echo "-- 查看包含AI提示词的迁移记录"
echo "SELECT id, source_table_name, target_table_name, "
echo "       LEFT(ai_prompt_description, 100) as ai_prompt_preview,"
echo "       LENGTH(ai_prompt_description) as prompt_length"
echo "FROM table_mapping_records "
echo "WHERE ai_prompt_description IS NOT NULL "
echo "ORDER BY created_time DESC LIMIT 3;"

echo ""
echo "-- 查看目标表的注释信息"
echo "SELECT TABLE_NAME, TABLE_COMMENT "
echo "FROM INFORMATION_SCHEMA.TABLES "
echo "WHERE TABLE_SCHEMA = 'health_car' "
echo "AND TABLE_COMMENT IS NOT NULL "
echo "AND TABLE_COMMENT != '' "
echo "ORDER BY CREATE_TIME DESC LIMIT 5;"

echo ""
echo "🎉 AI提示词功能测试完成！"
echo ""
echo "💡 如果发现问题："
echo "  1. 检查代码编译是否成功"
echo "  2. 确认应用已重启"
echo "  3. 查看应用日志中的AI优化相关信息"
echo "  4. 验证TableNameOptimizationService是否正常工作"
